 [AppOpenAd] Retrying with same network (attempt 1/2)
UltraFastLoader.tsx:317 [UltraFastLoader] ✅ Rendering MainNavigator for authenticated user
OptimizedFlatList.tsx:112 [OptimizedFlatList:HomeScreenFeed] Applied optimizations: {preset: 'FEED', itemHeight: undefined, numColumns: undefined, dataLength: 20, optimizations: Array(10)}
useQueries.ts:967 [useUsers] getNextPageParam: {totalRecords: 59473, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
useQueries.ts:967 [useUsers] getNextPageParam: {totalRecords: 59473, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
AppOpenAdManager.ts:120 🔄 [AppOpenAd] Retrying ad load after error...
BannerAdComponent.tsx:61 🔄 [BannerAd] Auto-rotating to next ad network
AdRotationService.ts:71 🔄 [AdRotation] Switched to Business Collaboration (rotation #7)
AdRotationService.ts:93 📱 [AdRotation] Next Business Collaboration banner ad: /***********,***********/com.adtip.app.adtip_app.Banner0.1750928844
AppOpenAdManager.ts:38 App open ad failed to load: NativeError: [googleMobileAds/null-activity] Ad attempted to load but the current Activity was null.
    at _handleAdEvent (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:138290:100)
    at apply (native)
    at emit (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2949:40)
    at anonymous (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:140786:100)
    at apply (native)
    at emit (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2949:40)
    at apply (native)
    at anonymous (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2811:200)
    at emit (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2827:66)
AppOpenAdManager.ts:99 ❌ [AppOpenAd] Other ad error: googleMobileAds/null-activity [googleMobileAds/null-activity] Ad attempted to load but the current Activity was null.
AppOpenAdManager.ts:117 🔄 [AppOpenAd] Retrying with same network (attempt 1/2)
useQueries.ts:967 [useUsers] getNextPageParam: {totalRecords: 59473, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
useQueries.ts:967 [useUsers] getNextPageParam: {totalRecords: 59473, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
App.tsx:414 App.tsx: Ensuring app open ad is loaded
AppOpenAdManager.ts:283 Force loading app open ad
UltraFastLoader.tsx:317 [UltraFastLoader] ✅ Rendering MainNavigator for authenticated user
OptimizedFlatList.tsx:112 [OptimizedFlatList:HomeScreenFeed] Applied optimizations: {preset: 'FEED', itemHeight: undefined, numColumns: undefined, dataLength: 20, optimizations: Array(10)}
useQueries.ts:967 [useUsers] getNextPageParam: {totalRecords: 59473, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
useQueries.ts:967 [useUsers] getNextPageParam: {totalRecords: 59473, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
AppOpenAdManager.ts:38 App open ad failed to load: NativeError: [googleMobileAds/null-activity] Ad attempted to load but the current Activity was null.
    at _handleAdEvent (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:138290:100)
    at apply (native)
    at emit (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2949:40)
    at anonymous (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:140786:100)
    at apply (native)
    at emit (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2949:40)
    at apply (native)
    at anonymous (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2811:200)
    at emit (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2827:66)
AppOpenAdManager.ts:99 ❌ [AppOpenAd] Other ad error: googleMobileAds/null-activity [googleMobileAds/null-activity] Ad attempted to load but the current Activity was null.
AppOpenAdManager.ts:117 🔄 [AppOpenAd] Retrying with same network (attempt 1/2)
AppOpenAdManager.ts:120 🔄 [AppOpenAd] Retrying ad load after error...
useQueries.ts:967 [useUsers] getNextPageParam: {totalRecords: 59473, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
useQueries.ts:967 [useUsers] getNextPageParam: {totalRecords: 59473, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
App.tsx:414 App.tsx: Ensuring app open ad is loaded
AppOpenAdManager.ts:283 Force loading app open ad
UltraFastLoader.tsx:317 [UltraFastLoader] ✅ Rendering MainNavigator for authenticated user
OptimizedFlatList.tsx:112 [OptimizedFlatList:HomeScreenFeed] Applied optimizations: {preset: 'FEED', itemHeight: undefined, numColumns: undefined, dataLength: 20, optimizations: Array(10)}
useQueries.ts:967 [useUsers] getNextPageParam: {totalRecords: 59473, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
useQueries.ts:967 [useUsers] getNextPageParam: {totalRecords: 59473, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
AppOpenAdManager.ts:38 App open ad failed to load: NativeError: [googleMobileAds/null-activity] Ad attempted to load but the current Activity was null.
    at _handleAdEvent (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:138290:100)
    at apply (native)
    at emit (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2949:40)
    at anonymous (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:140786:100)
    at apply (native)
    at emit (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2949:40)
    at apply (native)
    at anonymous (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2811:200)
    at emit (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2827:66)
AppOpenAdManager.ts:99 ❌ [AppOpenAd] Other ad error: googleMobileAds/null-activity [googleMobileAds/null-activity] Ad attempted to load but the current Activity was null.
AppOpenAdManager.ts:117 🔄 [AppOpenAd] Retrying with same network (attempt 1/2)
AppOpenAdManager.ts:120 🔄 [AppOpenAd] Retrying ad load after error...
useQueries.ts:967 [useUsers] getNextPageParam: {totalRecords: 59473, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
useQueries.ts:967 [useUsers] getNextPageParam: {totalRecords: 59473, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
App.tsx:414 App.tsx: Ensuring app open ad is loaded
AppOpenAdManager.ts:283 Force loading app open ad
UltraFastLoader.tsx:317 [UltraFastLoader] ✅ Rendering MainNavigator for authenticated user
OptimizedFlatList.tsx:112 [OptimizedFlatList:HomeScreenFeed] Applied optimizations: {preset: 'FEED', itemHeight: undefined, numColumns: undefined, dataLength: 20, optimizations: Array(10)}
useQueries.ts:967 [useUsers] getNextPageParam: {totalRecords: 59473, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
useQueries.ts:967 [useUsers] getNextPageParam: {totalRecords: 59473, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
AppOpenAdManager.ts:38 App open ad failed to load: NativeError: [googleMobileAds/null-activity] Ad attempted to load but the current Activity was null.
    at _handleAdEvent (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:138290:100)
    at apply (native)
    at emit (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2949:40)
    at anonymous (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:140786:100)
    at apply (native)
    at emit (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2949:40)
    at apply (native)
    at anonymous (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2811:200)
    at emit (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2827:66)
AppOpenAdManager.ts:99 ❌ [AppOpenAd] Other ad error: googleMobileAds/null-activity [googleMobileAds/null-activity] Ad attempted to load but the current Activity was null.
AppOpenAdManager.ts:117 🔄 [AppOpenAd] Retrying with same network (attempt 1/2)
AppOpenAdManager.ts:120 🔄 [AppOpenAd] Retrying ad load after error...
useQueries.ts:967 [useUsers] getNextPageParam: {totalRecords: 59473, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
useQueries.ts:967 [useUsers] getNextPageParam: {totalRecords: 59473, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
App.tsx:414 App.tsx: Ensuring app open ad is loaded
AppOpenAdManager.ts:283 Force loading app open ad
UltraFastLoader.tsx:317 [UltraFastLoader] ✅ Rendering MainNavigator for authenticated user
OptimizedFlatList.tsx:112 [OptimizedFlatList:HomeScreenFeed] Applied optimizations: {preset: 'FEED', itemHeight: undefined, numColumns: undefined, dataLength: 20, optimizations: Array(10)}
useQueries.ts:967 [useUsers] getNextPageParam: {totalRecords: 59473, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
useQueries.ts:967 [useUsers] getNextPageParam: {totalRecords: 59473, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
AppOpenAdManager.ts:38 App open ad failed to load: NativeError: [googleMobileAds/null-activity] Ad attempted to load but the current Activity was null.
    at _handleAdEvent (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:138290:100)
    at apply (native)
    at emit (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2949:40)
    at anonymous (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:140786:100)
    at apply (native)
    at emit (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2949:40)
    at apply (native)
    at anonymous (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2811:200)
    at emit (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2827:66)
AppOpenAdManager.ts:99 ❌ [AppOpenAd] Other ad error: googleMobileAds/null-activity [googleMobileAds/null-activity] Ad attempted to load but the current Activity was null.
AppOpenAdManager.ts:117 🔄 [AppOpenAd] Retrying with same network (attempt 1/2)
AppOpenAdManager.ts:120 🔄 [AppOpenAd] Retrying ad load after error...
useQueries.ts:967 [useUsers] getNextPageParam: {totalRecords: 59473, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
useQueries.ts:967 [useUsers] getNextPageParam: {totalRecords: 59473, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
App.tsx:414 App.tsx: Ensuring app open ad is loaded
AppOpenAdManager.ts:283 Force loading app open ad
UltraFastLoader.tsx:317 [UltraFastLoader] ✅ Rendering MainNavigator for authenticated user
OptimizedFlatList.tsx:112 [OptimizedFlatList:HomeScreenFeed] Applied optimizations: {preset: 'FEED', itemHeight: undefined, numColumns: undefined, dataLength: 20, optimizations: Array(10)}
useQueries.ts:967 [useUsers] getNextPageParam: {totalRecords: 59473, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
useQueries.ts:967 [useUsers] getNextPageParam: {totalRecords: 59473, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
AppOpenAdManager.ts:38 App open ad failed to load: NativeError: [googleMobileAds/null-activity] Ad attempted to load but the current Activity was null.
    at _handleAdEvent (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:138290:100)
    at apply (native)
    at emit (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2949:40)
    at anonymous (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:140786:100)
    at apply (native)
    at emit (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2949:40)
    at apply (native)
    at anonymous (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2811:200)
    at emit (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2827:66)
AppOpenAdManager.ts:99 ❌ [AppOpenAd] Other ad error: googleMobileAds/null-activity [googleMobileAds/null-activity] Ad attempted to load but the current Activity was null.
AppOpenAdManager.ts:117 🔄 [AppOpenAd] Retrying with same network (attempt 1/2)
AppOpenAdManager.ts:120 🔄 [AppOpenAd] Retrying ad load after error...
useQueries.ts:967 [useUsers] getNextPageParam: {totalRecords: 59473, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
useQueries.ts:967 [useUsers] getNextPageParam: {totalRecords: 59473, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
App.tsx:414 App.tsx: Ensuring app open ad is loaded
AppOpenAdManager.ts:283 Force loading app open ad
UltraFastLoader.tsx:317 [UltraFastLoader] ✅ Rendering MainNavigator for authenticated user
OptimizedFlatList.tsx:112 [OptimizedFlatList:HomeScreenFeed] Applied optimizations: {preset: 'FEED', itemHeight: undefined, numColumns: undefined, dataLength: 20, optimizations: Array(10)}
useQueries.ts:967 [useUsers] getNextPageParam: {totalRecords: 59473, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
useQueries.ts:967 [useUsers] getNextPageParam: {totalRecords: 59473, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
AppOpenAdManager.ts:38 App open ad failed to load: NativeError: [googleMobileAds/null-activity] Ad attempted to load but the current Activity was null.
    at _handleAdEvent (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:138290:100)
    at apply (native)
    at emit (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2949:40)
    at anonymous (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:140786:100)
    at apply (native)
    at emit (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2949:40)
    at apply (native)
    at anonymous (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2811:200)
    at emit (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:2827:66)
AppOpenAdManager.ts:99 ❌ [AppOpenAd] Other ad error: googleMobileAds/null-activity [googleMobileAds/null-activity] Ad attempted to load but the current Activity was null.
AppOpenAdManager.ts:117 🔄 [AppOpenAd] Retrying with same network (attempt 1/2)
AppOpenAdManager.ts:120 🔄 [AppOpenAd] Retrying ad load after error...
useQueries.ts:967 [useUsers] getNextPageParam: {totalRecords: 59473, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
useQueries.ts:967 [useUsers] getNextPageParam: {totalRecords: 59473, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
App.tsx:414 App.tsx: Ensuring app open ad is loaded
AppOpenAdManager.ts:283 Force loading app open ad
UltraFastLoader.tsx:317 [UltraFastLoader] ✅ Rendering MainNavigator 